<?php

declare(strict_types=1);

/** @var PDO $conexion */
global $conexion;

use App\classes\Hit;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/general/preparar.php';

$newhit = new Hit;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		if (isset($_GET['m'])) {
			$success_display = 'show';
			$success_text    = 'El hit ha sido modificado.';
		}
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get

#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
	try {
		// Set timezone to Bogotá, Colombia
		setTimeZoneCol();

		// Debug: Log the original pago value
		$originalPago = $_POST['pago'];
		$cleanedPago  = limpiar_datos($_POST['pago']);
		$floatPago    = (float)$cleanedPago;
		error_log("DEBUG - Original pago: " . var_export($originalPago, true));
		error_log("DEBUG - Cleaned pago: " . var_export($cleanedPago, true));
		error_log("DEBUG - Float pago: " . var_export($floatPago, true));

		// Use setters and cast pago to float
		$newhit->setDescripcion(limpiar_datos($_POST['descripcion']));

		// Clean requester field and remove "By " prefix if present
		$requester = limpiar_datos($_POST['requester']);
		if (stripos($requester, 'By ') === 0) {
			$requester = substr($requester, 3); // Remove "By " (3 characters)
		}
		$newhit->setRequester($requester);

		$newhit->setPago($floatPago);                    // Use the debugged float value
		$newhit->setNota(limpiar_datos($_POST['nota'])); // Set nota
		$newhit->setFecha(create_datetime());            // Set current datetime in Bogotá timezone

		// Use guardar method
		$newhit->guardar($conexion);

		$success_display = 'show';
		$success_text    = 'El hit ha sido ingresado.';

		$newhit = new Hit();

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_add

#region sub_delhit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delhit'])) {
	try {
		$delidhit = limpiar_datos($_POST['mdl_delhit_idhit']);

		Hit::delete($delidhit, $conexion);

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_delhit

#region sub_edithit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_edithit'])) {
	try {
		$_SESSION['idhit'] = limpiar_datos($_POST['selidhit']);

		header('Location: ehit');

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_edithit

#region sub_update_hit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_update_hit'])) {
	try {
		// Set timezone to Bogotá, Colombia
		setTimeZoneCol();

		$hitId = limpiar_datos($_POST['edit_hit_id']);
		$hit   = Hit::get($hitId, $conexion);

		if ($hit) {
			// Debug: Log the original edit pago value
			$originalEditPago = $_POST['edit_pago'];
			$cleanedEditPago  = limpiar_datos($_POST['edit_pago']);
			$floatEditPago    = (float)$cleanedEditPago;
			error_log("DEBUG EDIT - Original pago: " . var_export($originalEditPago, true));
			error_log("DEBUG EDIT - Cleaned pago: " . var_export($cleanedEditPago, true));
			error_log("DEBUG EDIT - Float pago: " . var_export($floatEditPago, true));

			// Update hit data using setters and cast pago to float
			$hit->setDescripcion(limpiar_datos($_POST['edit_descripcion']));

			// Clean requester field and remove "By " prefix if present
			$requester = limpiar_datos($_POST['edit_requester']);
			if (stripos($requester, 'By ') === 0) {
				$requester = substr($requester, 3); // Remove "By " (3 characters)
			}
			$hit->setRequester($requester);

			$hit->setPago($floatEditPago);                     // Use the debugged float value
			$hit->setNota(limpiar_datos($_POST['edit_nota'])); // Set nota

			// Handle fecha (start date/time) - combine date and time
			$fechaDate = limpiar_datos($_POST['edit_fecha_date']);
			$fechaTime = limpiar_datos($_POST['edit_fecha_time']);
			if (!empty($fechaDate) && !empty($fechaTime)) {
				$fechaDateTime = $fechaDate . ' ' . $fechaTime . ':00'; // Add seconds
				$hit->setFecha($fechaDateTime);
			}

			// Handle fecha_terminado (completion date/time) - combine date and time if provided
			$fechaTerminadoDate = limpiar_datos($_POST['edit_fecha_terminado_date'] ?? '');
			$fechaTerminadoTime = limpiar_datos($_POST['edit_fecha_terminado_time'] ?? '');
			if (!empty($fechaTerminadoDate) && !empty($fechaTerminadoTime)) {
				$fechaTerminadoDateTime = $fechaTerminadoDate . ' ' . $fechaTerminadoTime . ':00'; // Add seconds
				$hit->setFechaTerminado($fechaTerminadoDateTime);
			} else {
				// Clear fecha_terminado if not provided
				$hit->setFechaTerminado(null);
			}

			// Use guardar method to update
			$hit->guardar($conexion);

			$success_display = 'show';
			$success_text    = 'El hit ha sido actualizado.';
		} else {
			throw new Exception('Hit no encontrado.');
		}

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_update_hit

#region sub_finish_hit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_finish_hit'])) {
	try {
		// Set timezone to Bogotá, Colombia
		setTimeZoneCol();

		$hitId = limpiar_datos($_POST['finish_hit_id']);
		Hit::markAsFinished($hitId, $conexion);

		$success_display = 'show';
		$success_text    = 'El hit ha sido marcado como terminado.';

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_finish_hit

#region sub_return_hit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_return_hit'])) {
	try {
		$hitId = limpiar_datos($_POST['return_hit_id']);
		Hit::markAsReturned($hitId, $conexion);

		$success_display = 'show';
		$success_text    = 'El hit ha sido marcado como retornado.';

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_return_hit

#region AJAX handlers
// AJAX handler for finish hit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_finish_hit'])) {
	header('Content-Type: application/json');
	try {
		setTimeZoneCol();
		$hitId = limpiar_datos($_POST['hit_id']);
		Hit::markAsFinished($hitId, $conexion);
		echo json_encode(['success' => true, 'message' => 'Hit marcado como terminado']);
	} catch (Exception $e) {
		echo json_encode(['success' => false, 'message' => $e->getMessage()]);
	}
	exit;
}

// AJAX handler for return hit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_return_hit'])) {
	header('Content-Type: application/json');
	try {
		$hitId = limpiar_datos($_POST['hit_id']);
		Hit::markAsReturned($hitId, $conexion);
		echo json_encode(['success' => true, 'message' => 'Hit marcado como retornado']);
	} catch (Exception $e) {
		echo json_encode(['success' => false, 'message' => $e->getMessage()]);
	}
	exit;
}

// AJAX handler for get hit data
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_get_hit'])) {
	header('Content-Type: application/json');
	try {
		$hitId = limpiar_datos($_POST['hit_id']);
		$hit   = Hit::get($hitId, $conexion);
		if ($hit) {
			// Split fecha into date and time components for frontend
			$fechaDate = '';
			$fechaTime = '';
			if ($hit->getFecha()) {
				$fechaDateTime = new DateTime($hit->getFecha());
				$fechaDate = $fechaDateTime->format('Y-m-d');
				$fechaTime = $fechaDateTime->format('H:i');
			}

			// Split fecha_terminado into date and time components for frontend
			$fechaTerminadoDate = '';
			$fechaTerminadoTime = '';
			if ($hit->getFechaTerminado()) {
				$fechaTerminadoDateTime = new DateTime($hit->getFechaTerminado());
				$fechaTerminadoDate = $fechaTerminadoDateTime->format('Y-m-d');
				$fechaTerminadoTime = $fechaTerminadoDateTime->format('H:i');
			}

			echo json_encode([
				                 'success' => true,
				                 'data'    => [
					                 'id'                     => $hit->getId(),
					                 'descripcion'            => $hit->getDescripcion(),
					                 'requester'              => $hit->getRequester(),
					                 'pago'                   => $hit->getPago(),
					                 'nota'                   => $hit->getNota(),
					                 'fecha_date'             => $fechaDate,
					                 'fecha_time'             => $fechaTime,
					                 'fecha_terminado_date'   => $fechaTerminadoDate,
					                 'fecha_terminado_time'   => $fechaTerminadoTime,
					                 'terminado'              => $hit->getTerminado(),
					                 'estado'                 => $hit->getEstado()
				                 ]
			                 ]);
		} else {
			echo json_encode(['success' => false, 'message' => 'Hit no encontrado']);
		}
	} catch (Exception $e) {
		echo json_encode(['success' => false, 'message' => $e->getMessage()]);
	}
	exit;
}

// AJAX handler for get current active hit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_get_current_active_hit'])) {
	header('Content-Type: application/json');
	try {
		setTimeZoneCol(); // Set Bogotá timezone
		$hit = Hit::getCurrentActiveHit($conexion);
		if ($hit) {
			echo json_encode([
				                 'success'      => true,
				                 'data'         => [
					                 'id'          => $hit->getId(),
					                 'descripcion' => $hit->getDescripcion(),
					                 'requester'   => $hit->getRequester(),
					                 'pago'        => $hit->getPago(),
					                 'fecha'       => $hit->getFecha(),
					                 'nota'        => $hit->getNota()
				                 ],
				                 'current_time' => date('Y-m-d H:i:s') // Current server time in Bogotá timezone
			                 ]);
		} else {
			echo json_encode(['success' => false, 'message' => 'No hay hit activo']);
		}
	} catch (Exception $e) {
		echo json_encode(['success' => false, 'message' => $e->getMessage()]);
	}
	exit;
}

// AJAX handler for get goal progress data
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_get_goal_progress'])) {
	header('Content-Type: application/json');
	try {
		setTimeZoneCol(); // Set Bogotá timezone

		// Get current earnings
		$todays_earnings = Hit::getTodaysEarnings($conexion);
		$weekly_earnings = Hit::getWeeklyEarnings($conexion);
		$monthly_earnings = Hit::getMonthlyEarnings($conexion);

		// Get potential earnings from in-progress hits
		$todays_potential = Hit::getTodaysPotentialEarnings($conexion);
		$weekly_potential = Hit::getWeeklyPotentialEarnings($conexion);
		$monthly_potential = Hit::getMonthlyPotentialEarnings($conexion);

		// Get goal values
		$daily_goal = Config::getDailyGoal($conexion);
		$weekly_goal = Config::getWeeklyGoal($conexion);
		$monthly_goal = Config::getMonthlyGoal($conexion);

		// Calculate progress percentages
		$daily_progress = $daily_goal > 0 ? min(($todays_earnings / $daily_goal) * 100, 100) : 0;
		$weekly_progress = $weekly_goal > 0 ? min(($weekly_earnings / $weekly_goal) * 100, 100) : 0;
		$monthly_progress = $monthly_goal > 0 ? min(($monthly_earnings / $monthly_goal) * 100, 100) : 0;

		// Calculate potential progress percentages (what would happen if in-progress hits were completed)
		$daily_potential_progress = $daily_goal > 0 ? min(($todays_potential / $daily_goal) * 100, 100) : 0;
		$weekly_potential_progress = $weekly_goal > 0 ? min(($weekly_potential / $weekly_goal) * 100, 100) : 0;
		$monthly_potential_progress = $monthly_goal > 0 ? min(($monthly_potential / $monthly_goal) * 100, 100) : 0;

		echo json_encode([
			                 'success' => true,
			                 'data'    => [
				                 'daily'   => [
					                 'earnings'           => $todays_earnings,
					                 'goal'               => $daily_goal,
					                 'progress'           => round($daily_progress, 1),
					                 'percentage'         => round($daily_progress, 1) . '%',
					                 'potential_earnings' => $todays_potential,
					                 'potential_progress' => round($daily_potential_progress, 1)
				                 ],
				                 'weekly'  => [
					                 'earnings'           => $weekly_earnings,
					                 'goal'               => $weekly_goal,
					                 'progress'           => round($weekly_progress, 1),
					                 'percentage'         => round($weekly_progress, 1) . '%',
					                 'potential_earnings' => $weekly_potential,
					                 'potential_progress' => round($weekly_potential_progress, 1)
				                 ],
				                 'monthly' => [
					                 'earnings'           => $monthly_earnings,
					                 'goal'               => $monthly_goal,
					                 'progress'           => round($monthly_progress, 1),
					                 'percentage'         => round($monthly_progress, 1) . '%',
					                 'potential_earnings' => $monthly_potential,
					                 'potential_progress' => round($monthly_potential_progress, 1)
				                 ]
			                 ]
		                 ]);
	} catch (Exception $e) {
		echo json_encode(['success' => false, 'message' => $e->getMessage()]);
	}
	exit;
}
#endregion AJAX handlers

#region try
try {
	$hits   = Hit::getTodaysList($conexion);
	$trk_id = Config::getTrkId($conexion);
	$todays_earnings = Hit::getTodaysEarnings($conexion);
	$weekly_earnings = Hit::getWeeklyEarnings($conexion);
	$monthly_earnings = Hit::getMonthlyEarnings($conexion);

	// Get goal values for progress tracking
	$daily_goal = Config::getDailyGoal($conexion);
	$weekly_goal = Config::getWeeklyGoal($conexion);
	$monthly_goal = Config::getMonthlyGoal($conexion);

	// Get current active hit for dynamic title
	setTimeZoneCol(); // Set Bogotá timezone
	$current_active_hit = Hit::getCurrentActiveHit($conexion);

} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lhits.view.php';

?>
